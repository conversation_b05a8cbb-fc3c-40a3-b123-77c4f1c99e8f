@extends('layouts.auth')

@section('title', 'ورود به سیستم')

@section('content')
    <!-- login page start-->
    <div class="container-fluid">
        <div class="row">
            <div class="col-xl-7 d-none d-lg-block">
                <img class="bg-img-cover bg-center h-100" src="{{ asset('assets') }}/images/login/2.jpg" alt="login background">
            </div>
            <div class="col-xl-5 p-0" style="background-color: #F0EFFD;">
                <div class="login-card login-dark">
                    <div>
                        <div class="text-center">
                            <a class="text-center mt-5" href="/">
                                <img class="img-fluid for-light" style="width: 300px;" src="{{ asset('assets') }}/images/logo/logo.png" alt="زیگمامدیا">
                                <img class="img-fluid for-dark" style="width: 300px;" src="{{ asset('assets') }}/images/logo/logo_dark.png" alt="زیگمامدیا">
                            </a>
                        </div>
                        <div class="login-main">
                            @if ($errors->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if (session('status'))
                                <div class="alert alert-success">
                                    {{ session('status') }}
                                </div>
                            @endif

                            <form class="theme-form" method="POST" action="{{ route('login') }}">
                                @csrf
                                <h4>ورود به حساب کاربری</h4>
                                <p>ایمیل و رمز عبور خود را وارد کنید</p>

                                <div class="form-group" >
                                    <label class="col-form-label">آدرس ایمیل</label>
                                    <input class="form-control" type="email" name="email" style="direction: ltr;" value="{{ old('email') }}" required placeholder="<EMAIL>">
                                </div>

                                <div class="form-group">
                                    <label class="col-form-label">رمز عبور</label>
                                    <div class="form-input position-relative" style="direction: ltr;">
                                        <input class="form-control" type="password" name="password" required placeholder="*********">
                                        <div class="show-hide">
                                            <i class="fa fa-eye"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-0">
                                    <div class="checkbox p-0">
                                        <input id="checkbox1" type="checkbox" name="remember">
                                        <label class="text-muted " for="checkbox1">مرا به خاطر بسپار</label>
                                    </div>
                                    <a class="text-end" href="{{ route('password.request') }}">فراموشی رمز عبور؟</a>
                                    <div class="text-end mt-3">
                                        <button class="btn btn-primary btn-block w-100" type="submit">ورود</button>
                                    </div>
                                </div>

                                <p class="mt-4 mb-0 text-center">حساب کاربری ندارید؟
                                    <a class="ms-2" href="{{ route('register') }}">ثبت نام</a>
                                </p>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
