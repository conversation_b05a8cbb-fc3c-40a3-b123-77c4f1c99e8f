<!DOCTYPE html>
<html lang="fa" dir="rtl">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="زیگمامدیا - سیستم تبلیغات آنلاین">
    <meta name="keywords" content="تبلیغات، بازاریابی، زیگمامدیا">
    <meta name="author" content="zigmamedias">
    <link rel="icon" href="{{ asset('assets') }}/images/favicon.png" type="image/x-icon">
    <link rel="shortcut icon" href="{{ asset('assets') }}/images/favicon.png" type="image/x-icon">
    <title>@yield('title', 'احراز هویت') - زیگمامدیا</title>

    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/font-awesome.css">
    <!-- ico-font-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/vendors/icofont.css">
    <!-- Themify icon-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/vendors/themify.css">
    <!-- Flag icon-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/vendors/flag-icon.css">
    <!-- Feather icon-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/vendors/feather-icon.css">
    <!-- Plugins css start-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/vendors/scrollbar.css">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/vendors/animate.css">
    <!-- Plugins css Ends-->
    <!-- Bootstrap css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/vendors/bootstrap.css">
    <!-- App css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/style.css">
    <!-- Custom Logo CSS -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/custom.css">

    <link id="color" rel="stylesheet" href="{{ asset('assets') }}/css/color-1.css" media="screen">
    <!-- Responsive css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets') }}/css/responsive.css">

    <style>
        body {
            min-height: 100vh;
            font-family: 'Vazir', sans-serif;
        }

        .login-card {
            background-color: #ffffff;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-main {
            padding: 40px;
        }

        .theme-form h4 {
            margin-bottom: 10px;
            color: #017F7B;
            font-weight: 700;
            text-align: center;
        }

        .theme-form p {
            margin-bottom: 30px;
            color: #737373;
            text-align: center;
        }

        .form-control {
            border-radius: 12px;
            padding: 15px 20px;
            border: 2px solid #e6e6e6;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .form-control:focus {
            border-color: #017F7B;
            box-shadow: 0 0 0 0.2rem rgba(1, 127, 123, 0.25);
            background-color: #ffffff;
        }

        .btn-primary {
            background: linear-gradient(135deg, #017F7B 0%, #015c59 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(1, 127, 123, 0.4);
        }

        .show-hide {
            top: 18px;
            right: 20px;
            cursor: pointer;
        }

        .checkbox label {
            color: #737373;
        }

        .link {
            color: #017F7B;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .link:hover {
            color: #015c59;
            text-decoration: underline;
        }

        .login-card .logo {
            display: block;
            text-align: center;
            margin-bottom: 30px;
        }

        .login-card .logo img {
            max-width: 180px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 15px 20px;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border-left: 4px solid #dc3545;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border-left: 4px solid #28a745;
        }

        .form-input {
            position: relative;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .container-fluid {
            padding: 0;
        }

        .row {
            margin: 0;
            min-height: 100vh;
        }

        .col-xl-7 {
            position: relative;
            overflow: hidden;
        }

        .bg-img-cover {
            width: 100%;
            height: 100vh;
            object-fit: cover;
            filter: brightness(0.8);
        }

        .col-xl-5 {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        @media (max-width: 1199px) {
            .col-xl-7 {
                display: none;
            }
            
            .col-xl-5 {
                flex: 1;
                max-width: 100%;
            }
        }

        @media (max-width: 768px) {
            .login-main {
                padding: 30px 20px;
            }
            
            .login-card {
                margin: 10px;
            }
        }

        /* Loading animation */
        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .btn-primary.loading::after {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-left: 10px;
            border: 2px solid transparent;
            border-top: 2px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #017F7B;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #015c59;
        }
    </style>

    @stack('styles')
</head>

<body>
    @yield('content')

    <!-- latest jquery-->
    <script src="{{ asset('assets') }}/js/jquery.min.js"></script>
    <!-- Bootstrap js-->
    <script src="{{ asset('assets') }}/js/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- feather icon js-->
    <script src="{{ asset('assets') }}/js/icons/feather-icon/feather.min.js"></script>
    <script src="{{ asset('assets') }}/js/icons/feather-icon/feather-icon.js"></script>
    <!-- scrollbar js-->
    <!-- Sidebar jquery-->
    <script src="{{ asset('assets') }}/js/config.js"></script>
    <!-- Plugins JS start-->
    <!-- Plugins JS Ends-->
    <!-- Theme js-->
    <script src="{{ asset('assets') }}/js/script.js"></script>

    <script>
        $(document).ready(function() {
            // Show/hide password
            $('.show-hide').on('click', function() {
                $(this).toggleClass('active');
                const input = $(this).prev('input');
                if ($(this).hasClass('active')) {
                    input.attr('type', 'text');
                    $(this).html('<i class="fa fa-eye-slash"></i>');
                } else {
                    input.attr('type', 'password');
                    $(this).html('<i class="fa fa-eye"></i>');
                }
            });

            // Form submission loading state
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                submitBtn.addClass('loading').prop('disabled', true);
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>

    @stack('scripts')
</body>

</html>
